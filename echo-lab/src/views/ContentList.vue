<template>
    <div class="content-list">
        <div class="content-header">
            <h1>内容管理</h1>
            <div class="header-buttons">
                <el-button v-if="activeTab === 'contents'" type="primary" @click="goToEditor()">
                    <el-icon>
                        <i-ep-plus />
                    </el-icon>新建内容
                </el-button>
                <el-button v-if="activeTab === 'collections'" type="primary" @click="createCollection">
                    <el-icon>
                        <i-ep-plus />
                    </el-icon>新建合集
                </el-button>
            </div>
        </div>

        <!-- 浮动帮助按钮 -->
        <FloatingHelpButton page-type="content" />

        <!-- 标签页 -->
        <el-tabs v-model="activeTab" class="content-tabs" @tab-change="handleTabChange">
            <el-tab-pane label="内容" name="contents">
                <!-- 原有的内容管理界面 -->
                <div class="tab-content">
                    <!-- 搜索和过滤 -->
                    <div class="filter-section">
                        <el-input v-model="searchQuery" placeholder="搜索内容..." clearable class="search-input">
                            <template #prefix>
                                <el-icon>
                                    <i-ep-search />
                                </el-icon>
                            </template>
                        </el-input>

                        <el-select v-model="statusFilter" class="filter-item">
                            <el-option label="全部状态" value="all" />
                            <el-option label="草稿" value="draft" />
                            <el-option label="已发布" value="published" />
                        </el-select>



                        <el-select v-model="sortBy" class="filter-item">
                            <el-option label="更新时间" value="updatedAt" />
                            <el-option label="创建时间" value="createdAt" />
                            <el-option label="标题" value="title" />
                        </el-select>

                        <el-select v-model="sortOrder" class="filter-item">
                            <el-option label="降序" value="desc" />
                            <el-option label="升序" value="asc" />
                        </el-select>

                        <el-button type="primary" @click="handleSearch" :loading="contentStore.loading">
                            搜索
                        </el-button>
                    </div>

                    <!-- 内容列表 -->
                    <el-table v-loading="contentStore.loading" :data="contentStore.filteredContents"
                        :row-key="row => row.id" style="width: 100%">
                        <el-table-column label="缩略图" width="120">
                            <template #default="{ row }">
                                <div class="thumbnail-container">
                                    <ResponsiveImage v-if="row.thumbnailUrl" :src="row.thumbnailUrl" alt="缩略图"
                                        class="thumbnail-image" loading="lazy" />
                                    <div v-else class="thumbnail-placeholder">
                                        <el-icon :size="24">
                                            <i-ep-picture />
                                        </el-icon>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>

                        <el-table-column prop="name" label="标题" min-width="200">
                            <template #default="{ row }">
                                <el-link @click="goToEditor(row.id)" type="primary">{{ row.name }}</el-link>
                            </template>
                        </el-table-column>



                        <el-table-column prop="status" label="状态" width="100">
                            <template #default="{ row }">
                                <el-tag :type="row.status === 'published' ? 'success' : 'warning'">
                                    {{ row.status === 'published' ? '已发布' : '草稿' }}
                                </el-tag>
                            </template>
                        </el-table-column>



                        <el-table-column prop="filters" label="过滤器" width="250">
                            <template #default="{ row }">
                                <div v-if="row.filtersGrouped && Object.keys(row.filtersGrouped).length > 0" class="filter-group">
                                    <div v-for="(typeFilters, filterType) in row.filtersGrouped" :key="filterType" class="filter-type-group">
                                        <el-tag
                                            v-for="filter in typeFilters"
                                            :key="filter.id"
                                            size="small"
                                            class="filter-tag"
                                        >
                                            {{ filter.name }}
                                        </el-tag>
                                    </div>
                                </div>
                                <span v-else class="no-filters">-</span>
                            </template>
                        </el-table-column>

                        <el-table-column prop="createdAt" label="创建时间" width="180">
                            <template #default="{ row }">
                                {{ formatDate(row.createdAt) }}
                            </template>
                        </el-table-column>

                        <el-table-column prop="updatedAt" label="更新时间" width="180">
                            <template #default="{ row }">
                                {{ formatDate(row.updatedAt) }}
                            </template>
                        </el-table-column>

                        <el-table-column label="操作" width="200" fixed="right">
                            <template #default="{ row }">
                                <el-button-group>
                                    <el-button size="small" type="primary" @click="goToEditor(row.id)" title="编辑">
                                        <el-icon>
                                            <i-ep-edit />
                                        </el-icon>
                                    </el-button>
                                    <el-button size="small" type="success" @click="goToPlayer(row.id)" title="播放">
                                        <el-icon>
                                            <i-ep-video-play />
                                        </el-icon>
                                    </el-button>
                                    <!-- 上线/下架按钮 -->
                                    <el-button v-if="row.status === 'draft'" size="small" type="warning"
                                        @click="handlePublish(row)" title="上线">
                                        <el-icon>
                                            <i-ep-upload />
                                        </el-icon>
                                    </el-button>
                                    <el-button v-else size="small" type="info" @click="handleUnpublish(row)" title="下架">
                                        <el-icon>
                                            <i-ep-download />
                                        </el-icon>
                                    </el-button>
                                    <el-button size="small" type="danger" @click="handleDelete(row)" title="删除">
                                        <el-icon>
                                            <i-ep-delete />
                                        </el-icon>
                                    </el-button>
                                </el-button-group>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div class="pagination-section">
                        <el-pagination v-model:current-page="contentStore.pagination.page"
                            v-model:page-size="contentStore.pagination.pageSize"
                            :total="contentStore.pagination.total" :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
                            @current-change="handleCurrentChange" />
                    </div>
                </div>
            </el-tab-pane>

            <el-tab-pane label="合集" name="collections">
                <!-- 合集管理界面 -->
                <div class="tab-content">
                    <!-- 合集搜索和过滤 -->
                    <div class="filter-section">
                        <el-input v-model="collectionSearchQuery" placeholder="搜索合集..." clearable class="search-input">
                            <template #prefix>
                                <el-icon>
                                    <i-ep-search />
                                </el-icon>
                            </template>
                        </el-input>

                        <el-select v-model="collectionStatusFilter" class="filter-item">
                            <el-option label="全部状态" value="all" />
                            <el-option label="草稿" value="draft" />
                            <el-option label="已发布" value="published" />
                        </el-select>

                        <el-select v-model="collectionSortBy" class="filter-item">
                            <el-option label="更新时间" value="updated_at" />
                            <el-option label="创建时间" value="created_at" />
                            <el-option label="名称" value="name" />
                        </el-select>

                        <el-select v-model="collectionSortOrder" class="filter-item">
                            <el-option label="降序" value="desc" />
                            <el-option label="升序" value="asc" />
                        </el-select>

                        <el-button type="primary" @click="handleCollectionSearch" :loading="collectionStore.loading">
                            搜索
                        </el-button>
                    </div>

                    <!-- 合集网格 -->
                    <CollectionGrid :collections="collectionStore.collections" :loading="collectionStore.loading"
                        :show-actions="true" :show-pagination="false" empty-text="暂无合集，点击上方按钮创建第一个合集"
                        @edit="handleCollectionEdit" @delete="handleCollectionDelete" @publish="handleCollectionPublish"
                        @unpublish="handleCollectionUnpublish" @play="handleCollectionPlay" />
                </div>
            </el-tab-pane>
        </el-tabs>

        <!-- 删除确认对话框 -->
        <el-dialog v-model="deleteDialogVisible" title="确认删除" width="30%" destroy-on-close>
            <p>确定要删除 "{{ deleteItem?.name }}" 吗？此操作不可恢复。</p>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="deleteDialogVisible = false">取消</el-button>
                    <el-button type="danger" @click="confirmDelete">确定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 合集删除确认对话框 -->
        <el-dialog v-model="collectionDeleteDialogVisible" title="确认删除合集" width="30%" destroy-on-close>
            <p>确定要删除合集 "{{ deleteCollectionItem?.name }}" 吗？此操作不可恢复。</p>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="collectionDeleteDialogVisible = false">取消</el-button>
                    <el-button type="danger" @click="confirmCollectionDelete">确定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 创建合集对话框 -->
        <el-dialog v-model="createCollectionDialogVisible" title="创建合集" width="50%" destroy-on-close>
            <el-form :model="newCollectionForm" label-width="80px">
                <el-form-item label="合集名称" required>
                    <el-input v-model="newCollectionForm.name" placeholder="请输入合集名称" />
                </el-form-item>
                <el-form-item label="合集描述">
                    <el-input v-model="newCollectionForm.description" type="textarea" :rows="3" placeholder="请输入合集描述" />
                </el-form-item>
                <el-form-item label="封面图片">
                    <el-input v-model="newCollectionForm.coverImageUrl" placeholder="请输入封面图片URL" />
                </el-form-item>
                <el-form-item label="标签">
                    <el-input v-model="newCollectionForm.tags" placeholder="请输入标签，用逗号分隔" />
                </el-form-item>
                <el-form-item label="学习语言" required>
                    <el-select v-model="newCollectionForm.learningLanguage" placeholder="请选择学习语言" style="width: 100%">
                        <el-option
                            v-for="lang in availableLanguages"
                            :key="lang.value"
                            :label="lang.label"
                            :value="lang.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="公开设置">
                    <el-switch v-model="newCollectionForm.isPublic" active-text="公开" inactive-text="私有" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="createCollectionDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmCreateCollection">创建</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useContentStore } from '@/core/stores/contentStore';
import { useCollectionStore } from '@/stores/collectionStore';
import { useLanguageStore } from '@/stores/languageStore';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { ElMessage } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import { SUPPORTED_LANGUAGES } from '@/config/languages';

import CollectionGrid from '@/components/collection/CollectionGrid.vue';
import ResponsiveImage from '@/components/common/ResponsiveImage.vue';
import FloatingHelpButton from '@/components/help/FloatingHelpButton.vue';


const router = useRouter();
const route = useRoute();
const contentStore = useContentStore();
const collectionStore = useCollectionStore();
const languageStore = useLanguageStore();

// 标签页状态 - 从URL参数获取初始值
const activeTab = ref(route.query.tab || 'contents');

// 内容相关状态
const searchQuery = ref('');
const statusFilter = ref('all');

const sortBy = ref('updatedAt');
const sortOrder = ref('desc');
const deleteDialogVisible = ref(false);
const deleteItem = ref(null);

// 合集相关状态
const collectionSearchQuery = ref('');
const collectionStatusFilter = ref('all');
const collectionSortBy = ref('updated_at');
const collectionSortOrder = ref('desc');
const collectionDeleteDialogVisible = ref(false);
const deleteCollectionItem = ref(null);
const createCollectionDialogVisible = ref(false);
const newCollectionForm = ref({
    name: '',
    description: '',
    coverImageUrl: '',
    tags: '',
    isPublic: false,
    learningLanguage: languageStore.currentLearningLanguage || 'ja' // 默认使用当前学习语言
});

// 可用语言列表
const availableLanguages = computed(() => {
    return SUPPORTED_LANGUAGES.filter(lang => lang.status === 'available');
});

// 处理搜索
const handleSearch = () => {
    contentStore.updateFilters({
        search: searchQuery.value,
        status: statusFilter.value,
        sortBy: sortBy.value,
        sortOrder: sortOrder.value
    });
};

// 移除旧的分页监听，现在直接在事件处理函数中调用

// 处理合集搜索
const handleCollectionSearch = () => {
    // 重新获取合集数据，传递新的过滤参数
    collectionStore.fetchUserCollections({
        search: collectionSearchQuery.value,
        status: collectionStatusFilter.value,
        sortBy: collectionSortBy.value,
        sortOrder: collectionSortOrder.value,
        learningLanguage: languageStore.currentLearningLanguage, // 按当前学习语言过滤
        page: 1 // 重置到第一页
    });
};

// 处理标签页切换
const handleTabChange = (tabName) => {
    // 更新URL参数
    router.replace({
        path: route.path,
        query: { ...route.query, tab: tabName }
    });

    if (tabName === 'collections') {
        // 切换到合集标签页时加载合集数据
        collectionStore.fetchUserCollections({
            learningLanguage: languageStore.currentLearningLanguage // 按当前学习语言过滤
        });
    }
};

// 跳转到编辑器（新标签页）
const goToEditor = (id) => {
    if (id) {
        window.open(`/editor/${id}`, '_blank');
    } else {
        window.open('/editor', '_blank');
    }
};

// 跳转到播放器（新标签页）
const goToPlayer = (id) => {
    if (id) {
        window.open(`/player/${id}`, '_blank');
    }
};

// 处理删除
const handleDelete = (item) => {
    deleteItem.value = item;
    deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
    try {
        await contentStore.deleteContent(deleteItem.value.id);
        deleteDialogVisible.value = false;
        ElMessage.success('删除成功');
    } catch (error) {
        ElMessage.error('删除失败');
    }
};

// 处理发布（上线）
const handlePublish = async (item) => {
    try {
        await contentStore.publishContent(item.id);
        ElMessage.success('内容已上线');
    } catch (error) {
        ElMessage.error('上线失败: ' + error.message);
    }
};

// 处理下架
const handleUnpublish = async (item) => {
    try {
        await contentStore.unpublishContent(item.id);
        ElMessage.success('内容已下架');
    } catch (error) {
        ElMessage.error('下架失败: ' + error.message);
    }
};

// 处理分页变化
const handleSizeChange = (val) => {
    contentStore.updatePagination({ pageSize: val, page: 1 });
};

const handleCurrentChange = (val) => {
    contentStore.updatePagination({ page: val });
};

// 格式化日期
const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};







// 合集相关方法
const createCollection = () => {
    createCollectionDialogVisible.value = true;
};

const confirmCreateCollection = async () => {
    try {
        if (!newCollectionForm.value.name) {
            ElMessage.warning('请输入合集名称');
            return;
        }

        await collectionStore.createCollection(newCollectionForm.value);
        createCollectionDialogVisible.value = false;

        // 重置表单
        newCollectionForm.value = {
            name: '',
            description: '',
            coverImageUrl: '',
            tags: '',
            isPublic: false,
            learningLanguage: languageStore.currentLearningLanguage || 'ja'
        };

        ElMessage.success('合集创建成功');
    } catch (error) {
        ElMessage.error('创建合集失败: ' + error.message);
    }
};

const handleCollectionEdit = (collection) => {
    // 跳转到合集编辑页面
    router.push(`/collection/${collection.id}/edit`);
};

const handleCollectionDelete = (collection) => {
    deleteCollectionItem.value = collection;
    collectionDeleteDialogVisible.value = true;
};

const confirmCollectionDelete = async () => {
    try {
        await collectionStore.deleteCollection(deleteCollectionItem.value.id);
        collectionDeleteDialogVisible.value = false;
        ElMessage.success('合集删除成功');
    } catch (error) {
        ElMessage.error('删除合集失败: ' + error.message);
    }
};

const handleCollectionPublish = async (collection) => {
    try {
        await collectionStore.publishCollection(collection.id);
        ElMessage.success('合集已发布');
    } catch (error) {
        ElMessage.error('发布失败: ' + error.message);
    }
};

const handleCollectionUnpublish = async (collection) => {
    try {
        await collectionStore.unpublishCollection(collection.id);
        ElMessage.success('合集已下架');
    } catch (error) {
        ElMessage.error('下架失败: ' + error.message);
    }
};

const handleCollectionPlay = async (collection) => {
    try {
        // 如果合集已经有items信息，直接使用
        if (collection.items && collection.items.length > 0) {
            // 按排序顺序获取第一个内容
            const sortedItems = collection.items.sort((a, b) => a.sortOrder - b.sortOrder);
            const firstItem = sortedItems[0];

            if (firstItem.content) {
                // 直接跳转到播放器，传递合集信息
                router.push({
                    path: `/player/${firstItem.content.id}`,
                    query: {
                        collection: collection.id,
                        index: 0
                    }
                });
                return;
            }
        }

        // 如果没有items信息，获取合集详情
        await collectionStore.fetchCollectionById(collection.id);
        const currentCollection = collectionStore.currentCollection;

        if (currentCollection && currentCollection.items && currentCollection.items.length > 0) {
            // 按排序顺序获取第一个内容
            const sortedItems = currentCollection.items.sort((a, b) => a.sortOrder - b.sortOrder);
            const firstItem = sortedItems[0];

            if (firstItem.content) {
                // 直接跳转到播放器，传递合集信息
                router.push({
                    path: `/player/${firstItem.content.id}`,
                    query: {
                        collection: collection.id,
                        index: 0
                    }
                });
            } else {
                ElMessage.warning('合集中没有可播放的内容');
            }
        } else {
            ElMessage.warning('合集为空');
        }
    } catch (error) {
        console.error('播放合集失败:', error);
        ElMessage.error('播放失败，请稍后重试');
    }
};

// 初始化数据
onMounted(() => {
    contentStore.fetchContents();
    collectionStore.fetchUserCollections({
        learningLanguage: languageStore.currentLearningLanguage // 按当前学习语言过滤
    });
});
</script>

<style scoped>
.content-list {
    padding: 1.25rem;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
}

.content-tabs {
    margin-bottom: 1.25rem;
}

.tab-content {
    padding-top: 1rem;
}

.filter-section {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.25rem;
}

.search-input {
    width: 20rem;
}

.filter-item {
    width: 10rem;
}

.thumbnail-container {
    width: 100%;
    height: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    border-radius: 0.25rem;
    background-color: #f5f7fa;
}

.thumbnail-image {
    width: 100%;
    object-fit: cover;
}

.thumbnail-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #909399;
    background-color: #f5f7fa;
}

.tag-item {
    margin-right: 0.375rem;
}

.tag-group {
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
}

.tag-group-label {
    font-size: 12px;
    color: #909399;
    margin-right: 4px;
    white-space: nowrap;
}

.no-tags {
    color: #c0c4cc;
    font-style: italic;
}

.pagination-section {
    margin-top: 1.25rem;
    display: flex;
    justify-content: flex-end;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.625rem;
}
</style>