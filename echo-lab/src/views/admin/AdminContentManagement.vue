<template>
  <div class="admin-content-management">
    <div class="page-header">
      <h1>内容管理</h1>
      <p class="page-description">管理所有用户创建的内容</p>
    </div>

    <!-- 搜索和过滤 -->
    <div class="filter-section">
      <el-input 
        v-model="searchQuery" 
        placeholder="搜索内容..." 
        clearable 
        class="search-input"
        @keyup.enter="handleSearch"
        @clear="handleSearch"
      >
        <template #prefix>
          <el-icon><i-ep-search /></el-icon>
        </template>
        <template #append>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </template>
      </el-input>

      <el-select v-model="statusFilter" class="filter-item" @change="handleSearch">
        <el-option label="全部状态" value="all" />
        <el-option label="草稿" value="draft" />
        <el-option label="已发布" value="published" />
      </el-select>

      <el-select v-model="languageFilter" class="filter-item" @change="handleSearch">
        <el-option label="全部语言" value="all" />
        <el-option 
          v-for="lang in availableLanguages" 
          :key="lang.value" 
          :label="lang.label" 
          :value="lang.value" 
        />
      </el-select>

      <el-select v-model="sortBy" class="filter-item" @change="handleSearch">
        <el-option label="更新时间" value="updatedAt" />
        <el-option label="创建时间" value="createdAt" />
        <el-option label="标题" value="name" />
      </el-select>

      <el-select v-model="sortOrder" class="filter-item" @change="handleSearch">
        <el-option label="降序" value="desc" />
        <el-option label="升序" value="asc" />
      </el-select>
    </div>

    <!-- 内容列表 -->
    <div class="content-list" v-loading="loading">
      <div v-if="contents.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无内容" />
      </div>
      
      <div v-else class="content-grid">
        <div 
          v-for="content in contents" 
          :key="content.id" 
          class="content-card"
        >
          <div class="card-header">
            <div class="content-info">
              <h3 class="content-title">{{ content.name }}</h3>
              <p class="content-description">{{ content.description || '暂无描述' }}</p>
            </div>
            <div class="content-actions">
              <el-dropdown @command="handleAction">
                <el-button type="text" size="small">
                  <el-icon><i-ep-more /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'view', content}">查看</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'edit', content}">编辑</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'delete', content}" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div class="card-meta">
            <div class="meta-item">
              <el-icon><i-ep-user /></el-icon>
              <span>{{ content.creator?.username || content.userId }}</span>
            </div>
            <div class="meta-item">
              <el-icon><i-ep-calendar /></el-icon>
              <span>{{ formatDate(content.createdAt) }}</span>
            </div>
            <div class="meta-item">
              <el-tag :type="getStatusTagType(content.status)" size="small">
                {{ getStatusText(content.status) }}
              </el-tag>
            </div>
            <div class="meta-item">
              <el-tag size="small">{{ getLanguageLabel(content.learningLanguage) }}</el-tag>
            </div>
          </div>

          <div v-if="content.thumbnailUrl" class="card-thumbnail">
            <img :src="content.thumbnailUrl" :alt="content.name" />
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="total > 0" class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="确认删除"
      width="400px"
    >
      <p>确定要删除内容 "{{ deleteItem?.name }}" 吗？此操作不可恢复。</p>
      <template #footer>
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete" :loading="deleting">删除</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { SUPPORTED_LANGUAGES, getLanguageLabel } from '@/config/languages';
import adminContentService from '@/services/adminContentService';

const router = useRouter();

// 响应式数据
const contents = ref([]);
const loading = ref(false);
const deleting = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);

// 搜索和过滤
const searchQuery = ref('');
const statusFilter = ref('all');
const languageFilter = ref('all');
const sortBy = ref('updatedAt');
const sortOrder = ref('desc');

// 删除对话框
const deleteDialogVisible = ref(false);
const deleteItem = ref(null);

// 可用语言列表
const availableLanguages = computed(() => {
  return SUPPORTED_LANGUAGES.filter(lang => lang.status === 'available');
});

// 加载内容列表
const loadContents = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      search: searchQuery.value,
      status: statusFilter.value !== 'all' ? statusFilter.value : undefined,
      learningLanguage: languageFilter.value !== 'all' ? languageFilter.value : undefined,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
    };

    const response = await adminContentService.getAllContents(params);
    if (response.success) {
      contents.value = response.contents || [];
      total.value = response.pagination?.total || 0;
    } else {
      throw new Error(response.error || '获取内容列表失败');
    }
  } catch (error) {
    console.error('加载内容列表失败:', error);
    ElMessage.error(error.message || '加载内容列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadContents();
};

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize;
  currentPage.value = 1;
  loadContents();
};

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage;
  loadContents();
};

// 操作处理
const handleAction = ({ action, content }) => {
  switch (action) {
    case 'view':
      // 在新标签页打开内容详情
      window.open(`/content/${content.id}`, '_blank');
      break;
    case 'edit':
      // 跳转到编辑页面
      router.push(`/editor/${content.id}`);
      break;
    case 'delete':
      deleteItem.value = content;
      deleteDialogVisible.value = true;
      break;
  }
};

// 确认删除
const confirmDelete = async () => {
  if (!deleteItem.value) return;
  
  deleting.value = true;
  try {
    const response = await adminContentService.deleteContent(deleteItem.value.id);
    if (response.success) {
      ElMessage.success('删除成功');
      deleteDialogVisible.value = false;
      deleteItem.value = null;
      loadContents(); // 重新加载列表
    } else {
      throw new Error(response.error || '删除失败');
    }
  } catch (error) {
    console.error('删除内容失败:', error);
    ElMessage.error(error.message || '删除内容失败');
  } finally {
    deleting.value = false;
  }
};

// 工具函数
const formatDate = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleString('zh-CN');
};

const getStatusTagType = (status) => {
  switch (status) {
    case 'published': return 'success';
    case 'draft': return 'info';
    default: return '';
  }
};

const getStatusText = (status) => {
  switch (status) {
    case 'published': return '已发布';
    case 'draft': return '草稿';
    default: return status;
  }
};

// 初始化
onMounted(() => {
  loadContents();
});
</script>

<style scoped>
.admin-content-management {
  padding: 1.5rem;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: var(--el-text-color-secondary);
}

.filter-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 200px;
}

.filter-item {
  min-width: 120px;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.content-card {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 1rem;
  background: var(--el-bg-color);
  transition: box-shadow 0.2s;
}

.content-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.content-info {
  flex: 1;
}

.content-title {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.4;
}

.content-description {
  margin: 0;
  font-size: 0.875rem;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.card-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
}

.card-thumbnail {
  margin-top: 0.75rem;
}

.card-thumbnail img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
}

.pagination-section {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

.empty-state {
  text-align: center;
  padding: 3rem 0;
}

@media (max-width: 768px) {
  .admin-content-management {
    padding: 1rem;
  }
  
  .filter-section {
    flex-direction: column;
  }
  
  .search-input {
    min-width: auto;
  }
  
  .content-grid {
    grid-template-columns: 1fr;
  }
}
</style>
