<template>
  <div id="app" :class="{ 'mobile-device': isMobileDevice(), 'tablet-device': isTabletDevice() }">
    <router-view />
    <FeedbackButton v-if="showFeedbackButton" />
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import FeedbackButton from '@/components/common/FeedbackButton.vue';
import { isMobileDevice, isTabletDevice } from './utils/deviceDetector';

const route = useRoute();

// 在某些页面不显示反馈按钮
const showFeedbackButton = computed(() => {
  // 安全检查：确保路由对象和路径都存在
  try {
    if (!route || typeof route.path !== 'string') {
      return true; // 默认显示反馈按钮
    }

    // 在管理后台、登录页、反馈页面不显示反馈按钮
    const excludedPaths = ['/admin', '/login', '/feedback'];
    return !excludedPaths.some(path => route.path.startsWith(path));
  } catch (error) {
    console.warn('检查反馈按钮显示状态时出错:', error);
    return true; // 出错时默认显示
  }
});


</script>

<style>
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: Arial, Helvetica, sans-serif;
}

#app {
  height: 100vh;
  width: 100%;
}
</style>
