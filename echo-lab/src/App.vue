<template>
  <div id="app" :class="{ 'mobile-device': isMobileDevice(), 'tablet-device': isTabletDevice() }">
    <router-view />
    <FeedbackButton v-if="showFeedbackButton" />
  </div>
</template>

<script setup>
import FeedbackButton from '@/components/common/FeedbackButton.vue';
import { isMobileDevice, isTabletDevice } from './utils/deviceDetector';

// 简单显示反馈按钮，不依赖路由
const showFeedbackButton = true;


</script>

<style>
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: Arial, Helvetica, sans-serif;
}

#app {
  height: 100vh;
  width: 100%;
}
</style>
