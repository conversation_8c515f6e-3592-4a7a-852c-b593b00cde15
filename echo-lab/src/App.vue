<template>
  <div id="app" :class="{ 'mobile-device': isMobileDevice(), 'tablet-device': isTabletDevice() }">
    <router-view />
    <FeedbackButton v-if="showFeedbackButton" />
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import FeedbackButton from '@/components/common/FeedbackButton.vue';
import { isMobileDevice, isTabletDevice } from './utils/deviceDetector';

// 使用 try-catch 来安全地获取路由
let route;
try {
  route = useRoute();
} catch (error) {
  console.warn('路由尚未初始化:', error);
  route = null;
}

// 在某些页面不显示反馈按钮
const showFeedbackButton = computed(() => {
  // 确保路由已经初始化
  if (!route || !route.path) {
    return true; // 默认显示反馈按钮
  }

  // 在管理后台、登录页、反馈页面不显示反馈按钮
  const excludedPaths = ['/admin', '/login', '/feedback'];
  return !excludedPaths.some(path => route.path.startsWith(path));
});


</script>

<style>
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: Arial, Helvetica, sans-serif;
}

#app {
  height: 100vh;
  width: 100%;
}
</style>
