/**
 * 管理员内容控制器
 * 处理管理员对所有用户内容的管理请求
 */
const adminContentService = require("../services/adminContentService");

/**
 * 获取所有用户的内容列表
 * GET /api/admin/contents
 */
async function getAllContents(req, res) {
  try {
    const options = {
      page: parseInt(req.query.page) || 1,
      pageSize: parseInt(req.query.pageSize) || 20,
      search: req.query.search,
      status: req.query.status,
      learningLanguage: req.query.learningLanguage,
      sortBy: req.query.sortBy || "updated_at",
      sortOrder: req.query.sortOrder || "desc",
      userId: req.query.userId, // 可选：按用户ID过滤
    };

    const result = await adminContentService.getAllContents(options);

    res.json({
      success: true,
      ...result,
    });
  } catch (error) {
    console.error("获取所有内容列表失败:", error);
    res.status(500).json({
      success: false,
      error: `获取内容列表失败: ${error.message}`,
    });
  }
}

/**
 * 获取指定内容详情
 * GET /api/admin/contents/:id
 */
async function getContentDetail(req, res) {
  try {
    const { id } = req.params;
    const content = await adminContentService.getContentDetail(id);

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "内容不存在",
      });
    }

    res.json({
      success: true,
      content,
    });
  } catch (error) {
    console.error("获取内容详情失败:", error);
    res.status(500).json({
      success: false,
      error: `获取内容详情失败: ${error.message}`,
    });
  }
}

/**
 * 删除指定内容
 * DELETE /api/admin/contents/:id
 */
async function deleteContent(req, res) {
  try {
    const { id } = req.params;
    const adminUserId = req.user.id;

    await adminContentService.deleteContent(id, adminUserId);

    res.json({
      success: true,
      message: "内容删除成功",
    });
  } catch (error) {
    console.error("删除内容失败:", error);
    const statusCode = error.message.includes("不存在") ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      error: error.message,
    });
  }
}

/**
 * 更新内容状态
 * PATCH /api/admin/contents/:id/status
 */
async function updateContentStatus(req, res) {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const adminUserId = req.user.id;

    if (!status) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数: status",
      });
    }

    const content = await adminContentService.updateContentStatus(
      id,
      status,
      adminUserId
    );

    res.json({
      success: true,
      content,
      message: "内容状态更新成功",
    });
  } catch (error) {
    console.error("更新内容状态失败:", error);
    const statusCode = error.message.includes("不存在") ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      error: error.message,
    });
  }
}

/**
 * 批量删除内容
 * POST /api/admin/contents/batch-delete
 */
async function batchDeleteContents(req, res) {
  try {
    const { contentIds } = req.body;
    const adminUserId = req.user.id;

    if (!contentIds || !Array.isArray(contentIds) || contentIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数: contentIds (数组)",
      });
    }

    const result = await adminContentService.batchDeleteContents(
      contentIds,
      adminUserId
    );

    res.json({
      success: true,
      ...result,
      message: "批量删除完成",
    });
  } catch (error) {
    console.error("批量删除内容失败:", error);
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
}

/**
 * 批量更新内容状态
 * POST /api/admin/contents/batch-update-status
 */
async function batchUpdateContentStatus(req, res) {
  try {
    const { contentIds, status } = req.body;
    const adminUserId = req.user.id;

    if (!contentIds || !Array.isArray(contentIds) || contentIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数: contentIds (数组)",
      });
    }

    if (!status) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数: status",
      });
    }

    const result = await adminContentService.batchUpdateContentStatus(
      contentIds,
      status,
      adminUserId
    );

    res.json({
      success: true,
      ...result,
      message: "批量更新状态完成",
    });
  } catch (error) {
    console.error("批量更新内容状态失败:", error);
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
}

module.exports = {
  getAllContents,
  getContentDetail,
  deleteContent,
  updateContentStatus,
  batchDeleteContents,
  batchUpdateContentStatus,
};
