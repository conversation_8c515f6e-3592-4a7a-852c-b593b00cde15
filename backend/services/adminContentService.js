/**
 * 管理员内容服务
 * 提供管理员对所有用户内容的管理功能
 */
const db = require("../models");
const { nanoid } = require("nanoid");

class AdminContentService {
  /**
   * 获取所有用户的内容列表
   * @param {Object} options 查询选项
   * @returns {Object} 内容列表和分页信息
   */
  async getAllContents(options = {}) {
    const {
      page = 1,
      pageSize = 20,
      search = "",
      status,
      learningLanguage,
      sortBy = "updated_at",
      sortOrder = "DESC",
      userId,
    } = options;

    // 构建查询条件
    const where = {};

    if (search) {
      where[db.Sequelize.Op.or] = [
        { name: { [db.Sequelize.Op.like]: `%${search}%` } },
        { description: { [db.Sequelize.Op.like]: `%${search}%` } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (learningLanguage) {
      where.learningLanguage = learningLanguage;
    }

    if (userId) {
      where.userId = userId;
    }

    // 排序配置
    const order = [[sortBy, sortOrder.toUpperCase()]];

    // 分页配置
    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    try {
      // 查询内容
      const { count, rows } = await db.Content.findAndCountAll({
        where,
        order,
        offset,
        limit,
        include: [
          {
            model: db.User,
            as: "creator",
            attributes: ["id", "username", "email"],
            required: false,
          },
        ],
        distinct: true,
      });

      return {
        contents: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(count / pageSize),
        },
      };
    } catch (error) {
      console.error("获取所有内容列表失败:", error);
      throw new Error(`获取内容列表失败: ${error.message}`);
    }
  }

  /**
   * 获取指定内容详情
   * @param {string} contentId 内容ID
   * @returns {Object} 内容详情
   */
  async getContentDetail(contentId) {
    try {
      const content = await db.Content.findByPk(contentId, {
        include: [
          {
            model: db.User,
            as: "creator",
            attributes: ["id", "username", "email"],
            required: false,
          },
        ],
      });

      return content;
    } catch (error) {
      console.error("获取内容详情失败:", error);
      throw new Error(`获取内容详情失败: ${error.message}`);
    }
  }

  /**
   * 删除指定内容
   * @param {string} contentId 内容ID
   * @param {string} adminUserId 管理员用户ID
   */
  async deleteContent(contentId, adminUserId) {
    try {
      const content = await db.Content.findByPk(contentId);

      if (!content) {
        throw new Error("内容不存在");
      }

      // 记录管理员操作日志
      await this.logAdminAction(adminUserId, "delete_content", {
        contentId,
        contentName: content.name,
        originalUserId: content.userId,
      });

      // 删除内容
      await content.destroy();

      return { success: true };
    } catch (error) {
      console.error("删除内容失败:", error);
      throw error;
    }
  }

  /**
   * 更新内容状态
   * @param {string} contentId 内容ID
   * @param {string} status 新状态
   * @param {string} adminUserId 管理员用户ID
   */
  async updateContentStatus(contentId, status, adminUserId) {
    try {
      const content = await db.Content.findByPk(contentId);

      if (!content) {
        throw new Error("内容不存在");
      }

      const oldStatus = content.status;

      // 更新状态
      await content.update({ status });

      // 记录管理员操作日志
      await this.logAdminAction(adminUserId, "update_content_status", {
        contentId,
        contentName: content.name,
        oldStatus,
        newStatus: status,
        originalUserId: content.userId,
      });

      return content;
    } catch (error) {
      console.error("更新内容状态失败:", error);
      throw error;
    }
  }

  /**
   * 批量删除内容
   * @param {Array} contentIds 内容ID数组
   * @param {string} adminUserId 管理员用户ID
   */
  async batchDeleteContents(contentIds, adminUserId) {
    const transaction = await db.sequelize.transaction();

    try {
      const results = [];

      for (const contentId of contentIds) {
        try {
          const content = await db.Content.findByPk(contentId, { transaction });

          if (!content) {
            results.push({ contentId, success: false, error: "内容不存在" });
            continue;
          }

          // 记录管理员操作日志
          await this.logAdminAction(
            adminUserId,
            "batch_delete_content",
            {
              contentId,
              contentName: content.name,
              originalUserId: content.userId,
            },
            transaction
          );

          // 删除内容
          await content.destroy({ transaction });

          results.push({ contentId, success: true });
        } catch (error) {
          results.push({
            contentId,
            success: false,
            error: error.message,
          });
        }
      }

      await transaction.commit();

      const successCount = results.filter((r) => r.success).length;
      const failCount = results.filter((r) => !r.success).length;

      return {
        results,
        summary: {
          total: contentIds.length,
          success: successCount,
          failed: failCount,
        },
      };
    } catch (error) {
      await transaction.rollback();
      console.error("批量删除内容失败:", error);
      throw error;
    }
  }

  /**
   * 批量更新内容状态
   * @param {Array} contentIds 内容ID数组
   * @param {string} status 新状态
   * @param {string} adminUserId 管理员用户ID
   */
  async batchUpdateContentStatus(contentIds, status, adminUserId) {
    const transaction = await db.sequelize.transaction();

    try {
      const results = [];

      for (const contentId of contentIds) {
        try {
          const content = await db.Content.findByPk(contentId, { transaction });

          if (!content) {
            results.push({ contentId, success: false, error: "内容不存在" });
            continue;
          }

          const oldStatus = content.status;

          // 更新状态
          await content.update({ status }, { transaction });

          // 记录管理员操作日志
          await this.logAdminAction(
            adminUserId,
            "batch_update_content_status",
            {
              contentId,
              contentName: content.name,
              oldStatus,
              newStatus: status,
              originalUserId: content.userId,
            },
            transaction
          );

          results.push({ contentId, success: true });
        } catch (error) {
          results.push({
            contentId,
            success: false,
            error: error.message,
          });
        }
      }

      await transaction.commit();

      const successCount = results.filter((r) => r.success).length;
      const failCount = results.filter((r) => !r.success).length;

      return {
        results,
        summary: {
          total: contentIds.length,
          success: successCount,
          failed: failCount,
        },
      };
    } catch (error) {
      await transaction.rollback();
      console.error("批量更新内容状态失败:", error);
      throw error;
    }
  }

  /**
   * 记录管理员操作日志
   * @param {string} adminUserId 管理员用户ID
   * @param {string} action 操作类型
   * @param {Object} details 操作详情
   * @param {Object} transaction 数据库事务
   */
  async logAdminAction(adminUserId, action, details, transaction = null) {
    try {
      // 如果有管理员日志表，在这里记录
      // 暂时使用console.log记录
      console.log("管理员操作日志:", {
        adminUserId,
        action,
        details,
        timestamp: new Date().toISOString(),
      });

      // 如果有AdminLog模型，可以这样记录：
      // await db.AdminLog.create({
      //   id: nanoid(),
      //   adminUserId,
      //   action,
      //   details: JSON.stringify(details),
      //   createdAt: new Date()
      // }, { transaction });
    } catch (error) {
      console.error("记录管理员操作日志失败:", error);
      // 日志记录失败不应该影响主要操作
    }
  }
}

module.exports = new AdminContentService();
