/**
 * 管理员内容管理API路由
 * 提供管理员对所有用户内容的管理功能
 */
const express = require("express");
const router = express.Router();
const { authenticate } = require("../middleware/authMiddleware");
const { checkAdminPermission } = require("../middleware/adminMiddleware");
const adminContentController = require("../controllers/adminContentController");

/**
 * 获取所有用户的内容列表
 * GET /api/admin/contents
 */
router.get(
  "/",
  authenticate,
  checkAdminPermission,
  adminContentController.getAllContents
);

/**
 * 获取指定内容详情
 * GET /api/admin/contents/:id
 */
router.get(
  "/:id",
  authenticate,
  checkAdminPermission,
  adminContentController.getContentDetail
);

/**
 * 删除指定内容
 * DELETE /api/admin/contents/:id
 */
router.delete(
  "/:id",
  authenticate,
  checkAdminPermission,
  adminContentController.deleteContent
);

/**
 * 更新内容状态
 * PATCH /api/admin/contents/:id/status
 */
router.patch(
  "/:id/status",
  authenticate,
  checkAdminPermission,
  adminContentController.updateContentStatus
);

/**
 * 批量删除内容
 * POST /api/admin/contents/batch-delete
 */
router.post(
  "/batch-delete",
  authenticate,
  checkAdminPermission,
  adminContentController.batchDeleteContents
);

/**
 * 批量更新内容状态
 * POST /api/admin/contents/batch-update-status
 */
router.post(
  "/batch-update-status",
  authenticate,
  checkAdminPermission,
  adminContentController.batchUpdateContentStatus
);

module.exports = router;
