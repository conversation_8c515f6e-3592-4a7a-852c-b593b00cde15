-- 合集语言字段数据库更新脚本
-- 执行日期：2025-01-29
-- 说明：为合集表添加学习语言字段，支持按语言区分合集

-- 1. 添加 learning_language 字段
ALTER TABLE collections 
ADD COLUMN learning_language VARCHAR(10) NOT NULL DEFAULT 'ja' 
COMMENT '合集的目标学习语言，如ja、en、zh-CN等';

-- 2. 为现有数据设置默认语言（日语）
UPDATE collections 
SET learning_language = 'ja' 
WHERE learning_language IS NULL OR learning_language = '';

-- 3. 添加索引以提高查询性能
CREATE INDEX idx_collections_learning_language ON collections(learning_language);

-- 4. 添加复合索引（语言+状态+公开状态）
CREATE INDEX idx_collections_lang_status_public ON collections(learning_language, status, is_public);

-- 5. 验证迁移结果
SELECT 
    COUNT(*) as total_collections,
    learning_language,
    COUNT(*) as count_by_language
FROM collections 
GROUP BY learning_language;

-- 6. 显示表结构确认
DESCRIBE collections;
